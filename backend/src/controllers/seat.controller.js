import { asyncHand<PERSON> } from "../utils/asyncHandler.js";
import { ApiError } from "../utils/ApiError.js";
import { ApiResponse } from "../utils/ApiResponse.js";
import { Seat, TimeSlot, SeatBooking } from "../models/seat.model.js";
import { Center } from "../models/center.model.js";
import { User } from "../models/user.model.js";
import mongoose from "mongoose";
import QRCode from "qrcode";

// Helper function to generate QR code for a seat
const generateSeatQRCode = async (seatData) => {
    try {
        const qrData = {
            type: 'seat',
            seatId: seatData._id,
            centerId: seatData.center,
            seatNumber: seatData.seatNumber,
            row: seatData.row,
            column: seatData.column,
            timestamp: new Date().toISOString()
        };

        const qrCodeDataString = JSON.stringify(qrData);
        const qrCodeImage = await QRCode.toDataURL(qrCodeDataString, {
            errorCorrectionLevel: 'M',
            type: 'image/png',
            quality: 0.92,
            margin: 1,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        });

        return {
            qrCode: qrCodeImage,
            qrCodeData: qrCodeDataString
        };
    } catch (error) {
        console.error('Error generating QR code:', error);
        return {
            qrCode: null,
            qrCodeData: null
        };
    }
};

// Create seats for a center (admin only)
const createSeats = asyncHandler(async (req, res) => {
    try {
        console.log('Creating seats for center:', req.params.centerId);

        if (req.user.role !== "admin") {
            throw new ApiError(403, "Unauthorized access: Only admins can create seats");
        }

        const { centerId } = req.params;
        const { seats } = req.body; // Array of seat objects

        if (!seats || !Array.isArray(seats) || seats.length === 0) {
            throw new ApiError(400, "Seats array is required");
        }

        // Validate centerId is a valid ObjectId
        if (!mongoose.Types.ObjectId.isValid(centerId)) {
            throw new ApiError(400, "Invalid center ID");
        }

        console.log('Looking for center with ID:', centerId);
        const center = await Center.findById(centerId);
        console.log('Found center:', center ? center.name : 'null');
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        // Validate and create seats
        console.log('Creating seats for center:', centerId, 'Seats data:', seats);
        const createdSeats = [];
        for (const seatData of seats) {
            const { row, column, seatNumber, seatType, facilities, notes } = seatData;

            console.log('Processing seat:', { row, column, seatNumber, seatType });

            if (!row || !column) {
                throw new ApiError(400, "Row and column are required for each seat");
            }

            // Check if seat already exists
            const existingSeat = await Seat.findOne({ center: centerId, row, column });
            if (existingSeat) {
                throw new ApiError(400, `Seat ${row}${column} already exists in this center`);
            }

            const finalSeatNumber = seatNumber || `${row}${column}`;
            console.log('Using seat number:', finalSeatNumber);

            const seat = await Seat.create({
                seatNumber: finalSeatNumber,
                center: centerId,
                row,
                column,
                seatType: seatType || "regular",
                facilities: facilities || [],
                notes: notes || ""
            });

            // Generate QR code for the seat
            const qrCodeData = await generateSeatQRCode(seat);
            if (qrCodeData.qrCode) {
                seat.qrCode = qrCodeData.qrCode;
                seat.qrCodeData = qrCodeData.qrCodeData;
                await seat.save();
            }

            console.log('Created seat:', seat._id, seat.seatNumber);
            createdSeats.push(seat);
        }

        return res.status(201).json(
            new ApiResponse(201, createdSeats, "Seats created successfully")
        );
    } catch (error) {
        console.error('Detailed error in createSeats:', error);
        console.error('Error stack:', error.stack);
        throw new ApiError(error.statusCode || 500, error.message || "Failed to create seats");
    }
});

// Get all seats for a center
const getCenterSeats = asyncHandler(async (req, res) => {
    try {
        const { centerId } = req.params;
        const { includeInactive = false } = req.query;

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const filter = { center: centerId };
        if (!includeInactive) {
            filter.isActive = true;
        }

        const seats = await Seat.find(filter)
            .sort({ row: 1, column: 1 })
            .populate("center", "name location");

        return res.status(200).json(
            new ApiResponse(200, seats, "Seats fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch seats");
    }
});

// Create time slots for a center (admin only)
const createTimeSlots = asyncHandler(async (req, res) => {
    try {
        if (req.user.role !== "admin") {
            throw new ApiError(403, "Unauthorized access: Only admins can create time slots");
        }

        const { centerId } = req.params;
        const { name, startTime, endTime, daysOfWeek, maxBookingDuration, price } = req.body;

        if (!name || !startTime || !endTime || !daysOfWeek || !Array.isArray(daysOfWeek)) {
            throw new ApiError(400, "Name, start time, end time, and days of week are required");
        }

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const timeSlot = await TimeSlot.create({
            center: centerId,
            name,
            startTime,
            endTime,
            daysOfWeek,
            maxBookingDuration: maxBookingDuration || 180,
            price: price || 0
        });

        return res.status(201).json(
            new ApiResponse(201, timeSlot, "Time slot created successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to create time slot");
    }
});

// Get time slots for a center
const getCenterTimeSlots = asyncHandler(async (req, res) => {
    try {
        const { centerId } = req.params;
        const { includeInactive = false } = req.query;

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const filter = { center: centerId };
        if (!includeInactive) {
            filter.isActive = true;
        }

        const timeSlots = await TimeSlot.find(filter)
            .sort({ startTime: 1 })
            .populate("center", "name location");

        return res.status(200).json(
            new ApiResponse(200, timeSlots, "Time slots fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch time slots");
    }
});

// Get available seats for a specific date and time slot
const getAvailableSeats = asyncHandler(async (req, res) => {
    try {
        const { centerId, timeSlotId } = req.params;
        const { date, startTime, endTime } = req.query;

        if (!date || !startTime || !endTime) {
            throw new ApiError(400, "Date, start time, and end time are required");
        }

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        const timeSlot = await TimeSlot.findById(timeSlotId);
        if (!timeSlot) {
            throw new ApiError(404, "Time slot not found");
        }

        // Get all active seats for the center
        const allSeats = await Seat.find({ center: centerId, isActive: true })
            .sort({ row: 1, column: 1 });

        // Get existing bookings for the date and time range
        const bookingDate = new Date(date);
        const existingBookings = await SeatBooking.find({
            bookingDate,
            status: { $in: ["confirmed", "completed"] },
            $or: [
                {
                    startTime: { $lt: endTime },
                    endTime: { $gt: startTime }
                }
            ]
        }).populate("seat");

        // Filter out booked seats
        const bookedSeatIds = existingBookings.map(booking => booking.seat._id.toString());
        const availableSeats = allSeats.filter(seat => 
            !bookedSeatIds.includes(seat._id.toString())
        );

        return res.status(200).json(
            new ApiResponse(200, {
                availableSeats,
                totalSeats: allSeats.length,
                bookedSeats: existingBookings.length
            }, "Available seats fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch available seats");
    }
});

// Get available seats for a center without time slot requirement
const getAvailableSeatsSimple = asyncHandler(async (req, res) => {
    try {
        const { centerId } = req.params;
        const { date, startTime, endTime } = req.query;

        if (!date || !startTime || !endTime) {
            throw new ApiError(400, "Date, start time, and end time are required");
        }

        const center = await Center.findById(centerId);
        if (!center) {
            throw new ApiError(404, "Center not found");
        }

        // Get all active seats for the center
        const allSeats = await Seat.find({ center: centerId, isActive: true })
            .sort({ row: 1, column: 1 });

        // Get existing bookings for the date and time range
        const bookingDate = new Date(date);
        const existingBookings = await SeatBooking.find({
            bookingDate,
            status: { $in: ["confirmed", "completed"] },
            $or: [
                {
                    startTime: { $lt: endTime },
                    endTime: { $gt: startTime }
                }
            ]
        }).populate("seat");

        // Filter out booked seats
        const bookedSeatIds = existingBookings.map(booking => booking.seat._id.toString());
        const availableSeats = allSeats.filter(seat =>
            !bookedSeatIds.includes(seat._id.toString())
        );

        return res.status(200).json(
            new ApiResponse(200, {
                availableSeats,
                totalSeats: allSeats.length,
                bookedSeats: existingBookings.length
            }, "Available seats fetched successfully")
        );
    } catch (error) {
        throw new ApiError(error.statusCode || 500, error.message || "Failed to fetch available seats");
    }
});

// Handle QR code scan - get seat info and current booking status
const handleQRCodeScan = asyncHandler(async (req, res) => {
    try {
        const { qrData } = req.body;

        if (!qrData) {
            throw new ApiError(400, "QR code data is required");
        }

        let parsedData;
        try {
            parsedData = JSON.parse(qrData);
        } catch (error) {
            throw new ApiError(400, "Invalid QR code format");
        }

        if (parsedData.type !== 'seat' || !parsedData.seatId) {
            throw new ApiError(400, "Invalid seat QR code");
        }

        // Get seat information
        const seat = await Seat.findById(parsedData.seatId)
            .populate('center', 'name location image description');

        if (!seat) {
            throw new ApiError(404, "Seat not found");
        }

        if (!seat.isActive) {
            throw new ApiError(400, "This seat is currently inactive");
        }

        // Check current booking status for today
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        const currentBooking = await SeatBooking.findOne({
            seat: seat._id,
            bookingDate: {
                $gte: today,
                $lt: tomorrow
            },
            status: { $in: ['confirmed', 'completed'] }
        }).populate('user', 'fullName email');

        const response = {
            seat: {
                _id: seat._id,
                seatNumber: seat.seatNumber,
                row: seat.row,
                column: seat.column,
                seatType: seat.seatType,
                facilities: seat.facilities,
                center: seat.center
            },
            isBooked: !!currentBooking,
            currentBooking: currentBooking ? {
                _id: currentBooking._id,
                user: currentBooking.user,
                bookingDate: currentBooking.bookingDate,
                startTime: currentBooking.startTime,
                endTime: currentBooking.endTime,
                status: currentBooking.status,
                duration: currentBooking.duration
            } : null
        };

        return res.status(200).json(
            new ApiResponse(200, response, "QR code scanned successfully")
        );
    } catch (error) {
        console.error("Error handling QR code scan:", error);
        throw new ApiError(error.statusCode || 500, error.message || "Failed to process QR code");
    }
});

export {
    createSeats,
    getCenterSeats,
    createTimeSlots,
    getCenterTimeSlots,
    getAvailableSeats,
    getAvailableSeatsSimple,
    handleQRCodeScan
};
