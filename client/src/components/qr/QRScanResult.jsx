import React, { useState } from 'react';
import { toast } from 'react-hot-toast';
import customFetch from '../../utils/customFetch';

const QRScanResult = ({ scanData, onClose, onBookingSuccess }) => {
    const [isBooking, setIsBooking] = useState(false);
    const [bookingData, setBookingData] = useState({
        bookingDate: new Date().toISOString().split('T')[0],
        startTime: '09:00',
        endTime: '17:00',
        bookingNotes: ''
    });

    const { seat, isBooked, currentBooking } = scanData;

    const calculateDuration = () => {
        if (!bookingData.startTime || !bookingData.endTime) return 0;
        
        const start = new Date(`2000-01-01T${bookingData.startTime}:00`);
        const end = new Date(`2000-01-01T${bookingData.endTime}:00`);
        
        if (end <= start) return 0;
        
        return Math.floor((end - start) / (1000 * 60)); // Duration in minutes
    };

    const handleBookSeat = async () => {
        if (!bookingData.bookingDate || !bookingData.startTime || !bookingData.endTime) {
            toast.error('Please fill in all required fields');
            return;
        }

        const duration = calculateDuration();
        if (duration <= 0) {
            toast.error('End time must be after start time');
            return;
        }

        setIsBooking(true);
        try {
            const response = await customFetch('/seats/bookings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    seatId: seat._id,
                    bookingDate: bookingData.bookingDate,
                    startTime: bookingData.startTime,
                    endTime: bookingData.endTime,
                    duration: duration,
                    bookingNotes: bookingData.bookingNotes
                })
            });

            if (response.data.success) {
                toast.success('Seat booked successfully!');
                onBookingSuccess && onBookingSuccess(response.data.data);
                onClose();
            } else {
                toast.error('Failed to book seat');
            }
        } catch (error) {
            console.error('Error booking seat:', error);
            toast.error(error.response?.data?.message || 'Failed to book seat');
        } finally {
            setIsBooking(false);
        }
    };

    const formatDate = (dateString) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });
    };

    const formatTime = (timeString) => {
        return new Date(`2000-01-01T${timeString}:00`).toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: '2-digit',
            hour12: true
        });
    };

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                    <h3 className="text-xl font-semibold">Seat Information</h3>
                    <button
                        onClick={onClose}
                        className="text-gray-500 hover:text-gray-700 text-xl"
                    >
                        ✕
                    </button>
                </div>

                {/* Seat Details */}
                <div className="bg-gray-50 rounded-lg p-4 mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h4 className="font-semibold text-gray-800 mb-2">{seat.center.name}</h4>
                            <p className="text-gray-600 text-sm mb-1">📍 {seat.center.location}</p>
                            <p className="text-gray-600 text-sm">
                                Seat {seat.seatNumber} • Row {seat.row} • {seat.seatType}
                            </p>
                        </div>
                        <div className="flex items-center justify-end">
                            <div className={`px-3 py-1 rounded-full text-sm font-medium ${
                                isBooked 
                                    ? 'bg-red-100 text-red-800' 
                                    : 'bg-green-100 text-green-800'
                            }`}>
                                {isBooked ? 'Currently Booked' : 'Available'}
                            </div>
                        </div>
                    </div>
                </div>

                {isBooked ? (
                    /* Show current booking details */
                    <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-800">Current Booking Details</h4>
                        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <p className="text-sm text-gray-600">Booked by</p>
                                    <p className="font-medium">{currentBooking.user.fullName}</p>
                                    <p className="text-sm text-gray-500">{currentBooking.user.email}</p>
                                </div>
                                <div>
                                    <p className="text-sm text-gray-600">Date & Time</p>
                                    <p className="font-medium">{formatDate(currentBooking.bookingDate)}</p>
                                    <p className="text-sm text-gray-500">
                                        {formatTime(currentBooking.startTime)} - {formatTime(currentBooking.endTime)}
                                    </p>
                                </div>
                            </div>
                            <div className="mt-3 pt-3 border-t border-red-200">
                                <p className="text-sm text-gray-600">Status</p>
                                <p className="font-medium capitalize">{currentBooking.status}</p>
                            </div>
                        </div>
                        <div className="text-center">
                            <p className="text-gray-600 mb-4">This seat is currently unavailable for booking.</p>
                            <button
                                onClick={onClose}
                                className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                ) : (
                    /* Show booking form */
                    <div className="space-y-4">
                        <h4 className="text-lg font-semibold text-gray-800">Book This Seat</h4>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Booking Date *
                                </label>
                                <input
                                    type="date"
                                    value={bookingData.bookingDate}
                                    min={new Date().toISOString().split('T')[0]}
                                    onChange={(e) => setBookingData({
                                        ...bookingData,
                                        bookingDate: e.target.value
                                    })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Duration
                                </label>
                                <p className="text-sm text-gray-600 py-2">
                                    {Math.floor(calculateDuration() / 60)}h {calculateDuration() % 60}m
                                </p>
                            </div>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    Start Time *
                                </label>
                                <input
                                    type="time"
                                    value={bookingData.startTime}
                                    onChange={(e) => setBookingData({
                                        ...bookingData,
                                        startTime: e.target.value
                                    })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-1">
                                    End Time *
                                </label>
                                <input
                                    type="time"
                                    value={bookingData.endTime}
                                    onChange={(e) => setBookingData({
                                        ...bookingData,
                                        endTime: e.target.value
                                    })}
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    required
                                />
                            </div>
                        </div>

                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-1">
                                Notes (Optional)
                            </label>
                            <textarea
                                value={bookingData.bookingNotes}
                                onChange={(e) => setBookingData({
                                    ...bookingData,
                                    bookingNotes: e.target.value
                                })}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                rows="3"
                                placeholder="Any special requirements or notes..."
                            />
                        </div>

                        <div className="flex space-x-3 pt-4">
                            <button
                                onClick={onClose}
                                className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg hover:bg-gray-700 transition-colors"
                            >
                                Cancel
                            </button>
                            <button
                                onClick={handleBookSeat}
                                disabled={isBooking || calculateDuration() <= 0}
                                className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors disabled:bg-gray-400 disabled:cursor-not-allowed"
                            >
                                {isBooking ? 'Booking...' : 'Book Seat'}
                            </button>
                        </div>
                    </div>
                )}
            </div>
        </div>
    );
};

export default QRScanResult;
