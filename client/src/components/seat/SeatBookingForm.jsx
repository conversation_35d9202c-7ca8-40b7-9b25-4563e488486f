import React, { useState, useEffect } from 'react';
import { toast } from 'react-hot-toast';
import { seatAPI } from '../../services/seatAPI';
import SeatLayout from './SeatLayout';

const SeatBookingForm = ({ center, onBookingSuccess, preSelectedSeatId }) => {
    const [seats, setSeats] = useState([]);
    const [availableSeats, setAvailableSeats] = useState([]);
    const [selectedSeat, setSelectedSeat] = useState(null);
    const [bookingDate, setBookingDate] = useState('');
    const [startTime, setStartTime] = useState('');
    const [endTime, setEndTime] = useState('');
    const [bookingNotes, setBookingNotes] = useState('');
    const [loading, setLoading] = useState(false);
    const [loadingAvailability, setLoadingAvailability] = useState(false);

    useEffect(() => {
        if (center) {
            fetchSeats();
        }
    }, [center]);

    useEffect(() => {
        if (bookingDate && startTime && endTime) {
            fetchAvailableSeats();
        }
    }, [bookingDate, startTime, endTime]);

    // Handle pre-selected seat from QR code
    useEffect(() => {
        if (preSelectedSeatId && seats.length > 0) {
            const preSelectedSeat = seats.find(seat => seat._id === preSelectedSeatId);
            if (preSelectedSeat) {
                setSelectedSeat(preSelectedSeat);
                toast.success(`Seat ${preSelectedSeat.seatNumber} pre-selected from QR code!`);
            }
        }
    }, [preSelectedSeatId, seats]);



    const fetchSeats = async () => {
        try {
            const response = await seatAPI.getCenterSeats(center._id);
            setSeats(response.data.data || []);
        } catch (error) {
            console.error('Error fetching seats:', error);
            toast.error('Failed to fetch seats');
        }
    };

    const fetchAvailableSeats = async () => {
        if (!bookingDate || !startTime || !endTime) return;

        setLoadingAvailability(true);
        try {
            // Check for conflicting bookings directly
            const response = await seatAPI.getAvailableSeatsSimple(
                center._id,
                bookingDate,
                startTime,
                endTime
            );
            setAvailableSeats(response.data.data.availableSeats || []);

            // Reset selected seat if it's no longer available
            if (selectedSeat && !response.data.data.availableSeats.some(seat => seat._id === selectedSeat._id)) {
                setSelectedSeat(null);
            }
        } catch (error) {
            console.error('Error fetching available seats:', error);
            // If the new API doesn't exist yet, fall back to showing all seats
            setAvailableSeats(seats);
        } finally {
            setLoadingAvailability(false);
        }
    };



    const calculateDuration = () => {
        if (!startTime || !endTime) return 0;
        
        const start = new Date(`2000-01-01T${startTime}:00`);
        const end = new Date(`2000-01-01T${endTime}:00`);
        
        return Math.max(0, (end - start) / (1000 * 60)); // Duration in minutes
    };

    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!selectedSeat || !bookingDate || !startTime || !endTime) {
            toast.error('Please fill in all required fields');
            return;
        }

        const duration = calculateDuration();
        if (duration <= 0) {
            toast.error('End time must be after start time');
            return;
        }

        // Default maximum duration (8 hours)
        const maxDuration = 480;
        if (duration > maxDuration) {
            toast.error(`Booking duration cannot exceed ${maxDuration} minutes (8 hours)`);
            return;
        }

        setLoading(true);
        try {
            const bookingData = {
                seatId: selectedSeat._id,
                bookingDate,
                startTime,
                endTime,
                bookingNotes
            };

            const response = await seatAPI.createBooking(bookingData);
            toast.success('Seat booked successfully!');
            
            if (onBookingSuccess) {
                onBookingSuccess(response.data);
            }
            
            // Reset form
            setSelectedSeat(null);
            setBookingNotes('');
            
        } catch (error) {
            console.error('Error creating booking:', error);
            toast.error(error.response?.data?.message || 'Failed to book seat');
        } finally {
            setLoading(false);
        }
    };

    const getTodayDate = () => {
        return new Date().toISOString().split('T')[0];
    };

    return (
        <div className="seat-booking-form">
            <div className="bg-white rounded-lg shadow-md p-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-6">
                    {preSelectedSeatId ? 'Complete Your Booking' : 'Book a Seat'}
                </h2>

                {/* QR Code Booking Notice */}
                {preSelectedSeatId && selectedSeat && (
                    <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                        <div className="flex items-center">
                            <div className="flex-shrink-0">
                                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                </svg>
                            </div>
                            <div className="ml-3">
                                <h3 className="text-sm font-medium text-green-800">
                                    Seat Selected via QR Code
                                </h3>
                                <div className="mt-1 text-sm text-green-700">
                                    <p>Seat {selectedSeat.seatNumber} has been automatically selected. Fill in your booking details below.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                )}

                <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Center Info */}
                    <div className="bg-gray-50 p-4 rounded-lg">
                        <h3 className="font-semibold text-gray-800">{center.name}</h3>
                        <p className="text-gray-600">{center.location}</p>
                    </div>

                    {/* Date Selection */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Booking Date *
                        </label>
                        <input
                            type="date"
                            value={bookingDate}
                            onChange={(e) => setBookingDate(e.target.value)}
                            min={getTodayDate()}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            required
                        />
                    </div>



                    {/* Time Selection */}
                    <div className="grid grid-cols-2 gap-4">
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Start Time *
                            </label>
                            <input
                                type="time"
                                value={startTime}
                                onChange={(e) => setStartTime(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                End Time *
                            </label>
                            <input
                                type="time"
                                value={endTime}
                                onChange={(e) => setEndTime(e.target.value)}
                                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                required
                            />
                        </div>
                    </div>

                    {/* Duration Display */}
                    {startTime && endTime && (
                        <div className="text-sm text-gray-600">
                            Duration: {Math.floor(calculateDuration() / 60)}h {calculateDuration() % 60}m
                        </div>
                    )}

                    {/* Seat Layout - Hidden when seat is pre-selected from QR code */}
                    {bookingDate && startTime && endTime && !preSelectedSeatId && (
                        <div>
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                Select Seat *
                            </label>
                            {loadingAvailability ? (
                                <div className="text-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
                                    <p className="mt-2 text-gray-600">Checking availability...</p>
                                </div>
                            ) : (
                                <SeatLayout
                                    seats={seats}
                                    availableSeats={availableSeats}
                                    selectedSeat={selectedSeat}
                                    onSeatSelect={setSelectedSeat}
                                    showAvailabilityOnly={true}
                                />
                            )}
                        </div>
                    )}

                    {/* Pre-selected Seat Info - Shown when seat is selected from QR code */}
                    {preSelectedSeatId && selectedSeat && (
                        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 className="font-semibold text-blue-800 mb-2">Selected Seat (from QR Code)</h4>
                            <div className="text-sm text-blue-700">
                                <p><strong>Seat:</strong> {selectedSeat.seatNumber}</p>
                                <p><strong>Type:</strong> {selectedSeat.seatType}</p>
                                <p><strong>Row:</strong> {selectedSeat.row}</p>
                                {selectedSeat.facilities && selectedSeat.facilities.length > 0 && (
                                    <p><strong>Facilities:</strong> {selectedSeat.facilities.join(', ')}</p>
                                )}
                                {selectedSeat.notes && (
                                    <p><strong>Notes:</strong> {selectedSeat.notes}</p>
                                )}
                            </div>
                            <button
                                type="button"
                                onClick={() => {
                                    setSelectedSeat(null);
                                    // Remove the seatId from URL
                                    const url = new URL(window.location);
                                    url.searchParams.delete('seatId');
                                    window.history.replaceState({}, '', url);
                                }}
                                className="mt-3 text-sm text-blue-600 hover:text-blue-800 underline"
                            >
                                Choose a different seat
                            </button>
                        </div>
                    )}

                    {/* Booking Notes */}
                    <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                            Notes (Optional)
                        </label>
                        <textarea
                            value={bookingNotes}
                            onChange={(e) => setBookingNotes(e.target.value)}
                            rows={3}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="Any special requirements or notes..."
                        />
                    </div>

                    {/* Submit Button */}
                    <button
                        type="submit"
                        disabled={loading || !selectedSeat}
                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                        {loading ? 'Booking...' : 'Book Seat'}
                    </button>
                </form>
            </div>
        </div>
    );
};

export default SeatBookingForm;
